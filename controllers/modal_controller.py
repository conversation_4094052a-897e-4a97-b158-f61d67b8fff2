from flask import Blueprint, request, render_template
from services.modal_service import ModalService
from utils.result import success, error, bad_request
from decorators import login_required

modal_bp = Blueprint('modal', __name__, url_prefix='/modal')
modal_service = ModalService()

class ModalController:
    """模态数据控制器"""
    
    @staticmethod
    @modal_bp.route('/search')
    @login_required
    def search_page():
        """搜索页面"""
        return render_template('modal/modal_search.html')

    @staticmethod
    @modal_bp.route('/api/search')
    @login_required
    def search_modal_data():
        """搜索模态数据API"""
        params = request.args.to_dict()

        # 参数验证
        if not params.get('vehicle_model_id'):
            return bad_request("请选择车型")

        try:
            data = modal_service.search_modal_data(params)
            return success(data, "查询成功")
        except Exception as e:
            return error(f"查询失败: {str(e)}")

    @staticmethod
    @modal_bp.route('/api/data/<int:data_id>')
    @login_required
    def get_modal_detail(data_id):
        """获取模态数据详情"""
        try:
            data = modal_service.get_modal_detail(data_id)
            if not data:
                return error("数据不存在", 404)
            return success(data)
        except Exception as e:
            return error(f"获取详情失败: {str(e)}")

    @staticmethod
    @modal_bp.route('/api/vehicles')
    @login_required
    def get_vehicles():
        """获取车型列表"""
        try:
            vehicles = modal_service.get_vehicle_list()
            return success(vehicles)
        except Exception as e:
            return error(f"获取车型列表失败: {str(e)}")

    @staticmethod
    @modal_bp.route('/api/components')
    @login_required
    def get_components():
        """获取零部件列表"""
        try:
            vehicle_id = request.args.get('vehicle_id')
            components = modal_service.get_component_list(vehicle_id)
            return success(components)
        except Exception as e:
            return error(f"获取零部件列表失败: {str(e)}")
