from functools import wraps
from flask import session, request, redirect, url_for, jsonify
from utils.result import unauthorized

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 检查session中是否有用户信息
        if 'user' not in session:
            # 如果是API请求，返回JSON
            if request.is_json or request.path.startswith('/api/'):
                return unauthorized("用户未登录")
            # 如果是页面请求，重定向到登录页面
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """管理员权限验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user' not in session:
            if request.is_json or request.path.startswith('/api/'):
                return unauthorized("用户未登录")
            return redirect(url_for('login'))

        user_info = session.get('user_info', {})
        user_roles = user_info.get('roles', [])

        # 检查是否有管理员角色
        if 'admin' not in user_roles:
            if request.is_json or request.path.startswith('/api/'):
                return jsonify({'code': 403, 'message': '权限不足'}), 403
            return redirect(url_for('index'))

        return f(*args, **kwargs)
    return decorated_function
