{% extends "base.html" %}

{% block title %}首页 - NVH数据管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">欢迎使用NVH数据管理系统</h1>
</div>

<div class="row">
    <div class="col-12">
        <div class="welcome-section text-center py-5">
            <h2 class="mb-4">欢迎使用NVH数据管理系统</h2>
            <p class="lead text-muted mb-4">请从左侧菜单选择功能模块进行操作</p>

            <div class="row mt-5">
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-wave-square fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">模态数据查询</h5>
                            <p class="card-text">查询和分析车辆及零部件的模态测试数据</p>
                            <a href="{{ url_for('modal.search_page') }}" class="btn btn-primary">进入查询</a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-volume-up fa-3x text-success mb-3"></i>
                            <h5 class="card-title">声品质数据</h5>
                            <p class="card-text">声品质测试数据管理和分析</p>
                            <button class="btn btn-success" onclick="showComingSoon()">敬请期待</button>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-area fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">振动数据</h5>
                            <p class="card-text">振动测试数据管理和分析</p>
                            <button class="btn btn-warning" onclick="showComingSoon()">敬请期待</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
