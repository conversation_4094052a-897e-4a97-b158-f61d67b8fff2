from models import db
from models.modal_data_model import ModalDataModel
from models.vehicle_model import VehicleModel
from models.component_model import ComponentModel
from models.test_project_model import TestProjectModel

class ModalService:
    """模态数据业务逻辑"""
    
    def search_modal_data(self, params):
        """搜索模态数据"""
        vehicle_id = params.get('vehicle_model_id')
        component_id = params.get('component_id')
        
        query = db.session.query(
            ModalDataModel,
            TestProjectModel,
            VehicleModel,
            ComponentModel
        ).join(
            TestProjectModel, ModalDataModel.test_project_id == TestProjectModel.id
        ).join(
            VehicleModel, TestProjectModel.vehicle_model_id == VehicleModel.id
        ).outerjoin(
            ComponentModel, TestProjectModel.component_id == ComponentModel.id
        )
        
        # 筛选条件
        if vehicle_id:
            query = query.filter(VehicleModel.id == vehicle_id)
        if component_id:
            query = query.filter(ComponentModel.id == component_id)
        
        results = query.all()
        
        # 数据转换
        data_list = []
        for modal, project, vehicle, component in results:
            item = {
                'id': modal.id,
                'category': component.category if component else '整车',
                'sub_category': component.sub_category if component else '',
                'component_name': component.component_name if component else '整车',
                'frequency': float(modal.frequency),
                'mode_order': modal.mode_order,
                'mode_shape_description': modal.mode_shape_description,
                'direction': modal.direction,
                'damping_ratio': float(modal.damping_ratio) if modal.damping_ratio else None,
                'vehicle_name': vehicle.vehicle_model_name,
                'test_date': project.test_date.strftime('%Y-%m-%d'),
                'test_engineer': project.test_engineer,
                'test_condition': project.test_condition
            }
            data_list.append(item)
        
        return data_list
    
    def get_modal_detail(self, data_id):
        """获取模态数据详情"""
        modal_data = ModalDataModel.query.get(data_id)
        if not modal_data:
            return None
        
        # 关联查询
        project = TestProjectModel.query.get(modal_data.test_project_id)
        vehicle = VehicleModel.query.get(project.vehicle_model_id)
        component = ComponentModel.query.get(project.component_id) if project.component_id else None
        
        detail = modal_data.to_dict()
        detail.update({
            'project_info': project.to_dict(),
            'vehicle_info': vehicle.to_dict(),
            'component_info': component.to_dict() if component else None
        })
        
        return detail
    
    def get_vehicle_list(self):
        """获取车型列表"""
        vehicles = VehicleModel.query.filter_by(status='active').all()
        return [{'id': v.id, 'name': v.vehicle_model_name, 'code': v.vehicle_model_code} for v in vehicles]
    
    def get_component_list(self, vehicle_id=None):
        """获取零部件列表"""
        query = ComponentModel.query
        if vehicle_id:
            # 根据车型筛选零部件（通过测试项目关联）
            query = query.join(TestProjectModel).filter(TestProjectModel.vehicle_model_id == vehicle_id)
        
        components = query.distinct().all()
        return [{'id': c.id, 'name': c.component_name, 'category': c.category} for c in components]
