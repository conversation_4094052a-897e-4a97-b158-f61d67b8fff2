/**
 * 模态搜索页面管理类
 */
class ModalSearchManager {
    constructor() {
        this.vehicleSelect = document.getElementById('vehicle-select');
        this.componentSelect = document.getElementById('component-select');
        this.searchBtn = document.getElementById('search-btn');
        this.exportBtn = document.getElementById('export-btn');
        this.tableBody = document.getElementById('table-body');
        this.resultCount = document.getElementById('result-count');
        
        this.currentData = [];
        
        this.init();
    }

    /**
     * 初始化
     */
    init() {
        this.loadVehicles();
        this.bindEvents();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        this.vehicleSelect.addEventListener('change', () => {
            this.loadComponents();
        });
        
        this.searchBtn.addEventListener('click', () => {
            this.searchModalData();
        });
        
        this.exportBtn.addEventListener('click', () => {
            this.exportData();
        });
    }

    /**
     * 加载车型列表
     */
    async loadVehicles() {
        try {
            const result = await request.get('/modal/api/vehicles');
            this.renderVehicleOptions(result.data);
        } catch (error) {
            showMessage('加载车型列表失败: ' + error.message, 'error');
        }
    }

    /**
     * 加载零部件列表
     */
    async loadComponents() {
        const vehicleId = this.vehicleSelect.value;
        if (!vehicleId) {
            this.componentSelect.innerHTML = '<option value="">全部零件</option>';
            return;
        }
        
        try {
            const result = await request.get('/modal/api/components', { vehicle_id: vehicleId });
            this.renderComponentOptions(result.data);
        } catch (error) {
            showMessage('加载零部件列表失败: ' + error.message, 'error');
        }
    }

    /**
     * 搜索模态数据
     */
    async searchModalData() {
        const params = {
            vehicle_model_id: this.vehicleSelect.value,
            component_id: this.componentSelect.value
        };
        
        if (!params.vehicle_model_id) {
            showMessage('请选择车型', 'warning');
            return;
        }
        
        // 显示加载状态
        this.showTableLoading();
        this.searchBtn.disabled = true;
        
        try {
            const result = await request.get('/modal/api/search', params);
            this.currentData = result.data;
            this.renderTable(result.data);
            this.updateResultCount(result.data.length);
            this.exportBtn.disabled = result.data.length === 0;
            showMessage('查询成功', 'success');
        } catch (error) {
            this.showTableError('查询失败: ' + error.message);
            showMessage('查询失败: ' + error.message, 'error');
        } finally {
            this.searchBtn.disabled = false;
        }
    }

    /**
     * 渲染车型选项
     */
    renderVehicleOptions(vehicles) {
        this.vehicleSelect.innerHTML = '<option value="">请选择车型</option>';
        vehicles.forEach(vehicle => {
            const option = document.createElement('option');
            option.value = vehicle.id;
            option.textContent = vehicle.name;
            this.vehicleSelect.appendChild(option);
        });
    }

    /**
     * 渲染零部件选项
     */
    renderComponentOptions(components) {
        this.componentSelect.innerHTML = '<option value="">全部零件</option>';
        components.forEach(component => {
            const option = document.createElement('option');
            option.value = component.id;
            option.textContent = component.name;
            this.componentSelect.appendChild(option);
        });
    }

    /**
     * 渲染数据表格
     */
    renderTable(dataList) {
        if (dataList.length === 0) {
            this.showTableEmpty();
            return;
        }
        
        this.tableBody.innerHTML = '';
        
        dataList.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.category}</td>
                <td>${item.sub_category}</td>
                <td>${item.component_name}</td>
                <td class="frequency-value">${formatNumber(item.frequency, 1)}</td>
                <td>${item.mode_order}</td>
                <td>${item.mode_shape_description || '-'}</td>
                <td>
                    <button class="btn btn-sm btn-primary btn-action" onclick="modalSearchManager.showModeShape(${item.id})">
                        <i class="fas fa-wave-square me-1"></i>查看振型
                    </button>
                </td>
            `;
            this.tableBody.appendChild(row);
        });
    }

    /**
     * 显示表格加载状态
     */
    showTableLoading() {
        this.tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <div class="loading-spinner mb-2"></div>
                    <div class="text-muted">正在搜索数据...</div>
                </td>
            </tr>
        `;
    }

    /**
     * 显示表格空状态
     */
    showTableEmpty() {
        this.tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    没有找到符合条件的数据
                </td>
            </tr>
        `;
    }

    /**
     * 显示表格错误状态
     */
    showTableError(message) {
        this.tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-danger py-4">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i><br>
                    ${message}
                </td>
            </tr>
        `;
    }

    /**
     * 更新结果计数
     */
    updateResultCount(count) {
        this.resultCount.textContent = `${count} 条记录`;
    }

    /**
     * 显示振型
     */
    async showModeShape(dataId) {
        try {
            const result = await request.get(`/modal/api/data/${dataId}`);
            this.renderModeShapeModal(result.data);

            const modal = new bootstrap.Modal(document.getElementById('modal-detail-modal'));
            modal.show();
        } catch (error) {
            showMessage('获取振型数据失败: ' + error.message, 'error');
        }
    }

    /**
     * 渲染振型查看弹窗
     */
    renderModeShapeModal(data) {
        const content = document.getElementById('modal-detail-content');

        content.innerHTML = `
            <!-- 基本信息行 -->
            <div class="mode-shape-info-row">
                <div class="info-item">
                    <span class="info-label">车型:</span>
                    <span class="info-value">${data.vehicle_info.vehicle_model_name}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">零件:</span>
                    <span class="info-value">${data.component_info ? data.component_info.component_name : '-'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">频率:</span>
                    <span class="info-value">${formatNumber(data.frequency, 1)} Hz</span>
                </div>
            </div>

            <div class="mode-shape-info-row">
                <div class="info-item">
                    <span class="info-label">阶次:</span>
                    <span class="info-value">${data.mode_order}</span>
                </div>
            </div>

            <!-- 图片展示区域 -->
            <div class="mode-shape-tabs">
                <div class="tabs-header">
                    <button class="tab-button active" data-target="shape-pane">
                        振型动画
                    </button>
                    <button class="tab-button" data-target="photo-pane">
                        测试照片
                    </button>
                </div>
                <div class="tab-content-area">
                    <div class="tab-pane active" id="shape-pane">
                        <div class="image-container">
                            ${data.mode_shape_file ? `
                                <img src="${data.mode_shape_file}" alt="振型动画" class="mode-shape-image">
                            ` : `
                                <div class="no-image-placeholder">
                                    <i class="fas fa-image fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">暂无振型动画</p>
                                </div>
                            `}
                        </div>
                    </div>
                    <div class="tab-pane" id="photo-pane">
                        <div class="image-container">
                            ${data.test_photo_file ? `
                                <img src="${data.test_photo_file}" alt="测试照片" class="mode-shape-image">
                            ` : `
                                <div class="no-image-placeholder">
                                    <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">暂无测试照片</p>
                                </div>
                            `}
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 初始化标签页切换功能
        this.initTabSwitching();
    }

    /**
     * 初始化标签页切换功能
     */
    initTabSwitching() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetId = button.getAttribute('data-target');

                // 移除所有活动状态
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabPanes.forEach(pane => pane.classList.remove('active'));

                // 添加当前活动状态
                button.classList.add('active');
                document.getElementById(targetId).classList.add('active');
            });
        });
    }

    /**
     * 导出数据
     */
    exportData() {
        if (this.currentData.length === 0) {
            showMessage('没有数据可导出', 'warning');
            return;
        }
        
        // 简单的CSV导出
        const headers = ['分类', '子分类', '零件名称', '频率(Hz)', '阶次', '模态描述'];
        const csvContent = [
            headers.join(','),
            ...this.currentData.map(item => [
                item.category,
                item.sub_category,
                item.component_name,
                item.frequency,
                item.mode_order,
                item.mode_shape_description || ''
            ].join(','))
        ].join('\n');
        
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `模态数据_${new Date().toISOString().slice(0, 10)}.csv`;
        link.click();
        
        showMessage('数据导出成功', 'success');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.modalSearchManager = new ModalSearchManager();
});
